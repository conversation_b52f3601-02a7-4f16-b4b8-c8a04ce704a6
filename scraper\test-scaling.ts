// Test script to verify worker scaling functionality
import { WorkerPool } from "./workers/workerPool";
import { logger } from "./utils/logger";

async function testWorkerScaling() {
  logger.info("🧪 Starting worker scaling test...");

  try {
    // Create a worker pool with initial max of 2 workers
    const workerPool = new WorkerPool({
      maxWorkers: 2,
      headless: true,
      recycleThreshold: 50,
    });

    // Test 1: Check initial status
    let status = workerPool.getWorkerPoolStatus();
    logger.info(`📊 Initial status: ${JSON.stringify(status)}`);

    // Test 2: Scale up to 5 workers
    logger.info("🔄 Testing scale up to 5 workers...");
    await workerPool.setMaxWorkers(5);
    status = workerPool.getWorkerPoolStatus();
    logger.info(`📊 After scale up: ${JSON.stringify(status)}`);

    // Test 3: Scale up to 8 workers (beyond initial max)
    logger.info("🔄 Testing scale up to 8 workers (beyond initial max)...");
    await workerPool.setMaxWorkers(8);
    status = workerPool.getWorkerPoolStatus();
    logger.info(`📊 After scale up to 8: ${JSON.stringify(status)}`);

    // Test 4: Scale down to 3 workers
    logger.info("🔄 Testing scale down to 3 workers...");
    await workerPool.setMaxWorkers(3);
    status = workerPool.getWorkerPoolStatus();
    logger.info(`📊 After scale down: ${JSON.stringify(status)}`);

    // Test 5: Scale up to 15 workers (well beyond initial max)
    logger.info("🔄 Testing scale up to 15 workers (well beyond initial max)...");
    await workerPool.setMaxWorkers(15);
    status = workerPool.getWorkerPoolStatus();
    logger.info(`📊 After scale up to 15: ${JSON.stringify(status)}`);

    logger.info("✅ Worker scaling test completed successfully");

    // Clean up
    await workerPool.shutdown();
    logger.info("🧹 Worker pool shut down");

  } catch (error) {
    logger.error(`❌ Worker scaling test failed: ${error}`);
    throw error;
  }
}

// Run the test
testWorkerScaling()
  .then(() => {
    logger.info("🎉 All tests passed!");
    process.exit(0);
  })
  .catch((error) => {
    logger.error(`💥 Test failed: ${error}`);
    process.exit(1);
  });
