import { logger } from "../utils/logger";
import { searchBingForJobs } from "../lib/search/bingJobSearch";
import { WorkerPool } from "./workerPool";
import { JobBatch, Worker } from "../utils/types";
import { delay } from "../utils/humanBehavior";
import { ProgressTracker } from "../services/progressTracker";
import { JobService } from "../services/jobService";
import { CaptchaService } from "../services/captchaService";
import { JobData } from "../types/jobData";
import { getPrismaClient } from "../utils/prismaClient";

export async function processJobBatch(
  workerPool: WorkerPool,
  batch: JobBatch
): Promise<{
  occupationTitle: string | null;
  cityName: string | null;
  stateCode: string | null;
  batchId: string;
  success: boolean;
  jobsFound: number;
  jobsSaved: number;
  lastProcessedOccupationId?: string;
  completedOccupationIds?: string[];
  error?: string;
}> {
  // Step 1: Get the specific worker assigned to this batch
  let worker = await workerPool.getSpecificWorker(batch.workerIndex);

  // Retry up to 3 times if no worker is available
  let retryCount = 0;
  const maxRetries = 3;

  while (!worker && retryCount < maxRetries) {
    retryCount++;
    logger.warn(
      `⚠️ Failed to get a worker for batch ${batch.batchId}, retrying (${retryCount}/${maxRetries})...`
    );

    // Wait a bit before retrying
    await new Promise((resolve) => setTimeout(resolve, 5000 * retryCount));

    // Try to get any available worker
    worker = await workerPool.getSpecificWorker(batch.workerIndex);
  }

  if (!worker) {
    logger.error(
      `❌ Failed to get a worker for batch ${batch.batchId} after ${maxRetries} retries`
    );
    return {
      batchId: batch.batchId,
      occupationTitle: null,
      cityName: batch.cityName,
      stateCode: batch.stateCode,
      success: false,
      jobsFound: 0,
      jobsSaved: 0,
      error: "No worker available after multiple retries",
      // Include all occupations in the batch as completed to avoid getting stuck
      completedOccupationIds: batch.occupations.map((o) => String(o.id)),
    };
  }

  logger.info(`✅ Got worker #${worker.id} for batch ${batch.batchId}`);

  worker.currentBatch = batch;
  worker.currentJob = {
    title: batch.occupations[0].title,
    id: batch.occupations[0].id,
  };

  try {
    // Step 2: Verify the global Prisma client is available
    try {
      getPrismaClient(); // Just verify it's available
      logger.info(
        `✅ Global Prisma client is available for worker #${worker.id}`
      );
    } catch (error) {
      logger.error("❌ Global Prisma client is not available:", error);
      throw new Error("Global Prisma client is not available");
    }

    const jobService = new JobService();

    // Debug: Check if JobService was properly initialized
    if (!jobService) {
      logger.error("❌ JobService is undefined after initialization");
      throw new Error("Failed to initialize JobService");
    }

    logger.info(
      `✅ JobService initialized successfully for worker #${worker.id}`
    );

    let allJobs: any[] = [];
    let totalJobsFound = 0;
    let lastProcessedOccupationId: string | undefined = undefined;

    // We'll navigate to Bing first in the processJobForOccupation function
    logger.info(`🌐 Worker #${worker.id} going to Bing Jobs...`);

    // We'll build browser history in the processJobForOccupation function
    // This ensures it happens before any navigation attempts

    // Step 3: Iterate over occupations in the batch and process jobs
    for (const occupation of batch.occupations) {
      logger.info(
        `🔍 Scraping jobs for ${occupation.title} in ${batch.cityName}`
      );

      // Update the current job being processed
      worker.currentJob = {
        title: occupation.title,
        id: occupation.id,
      };

      // Update the last processed occupation ID
      lastProcessedOccupationId = occupation.id;
      logger.info(
        `💾 Updated lastProcessedOccupationId to: ${lastProcessedOccupationId}`
      );

      // We'll save progress after processing the job

      // Use our improved processJobForOccupation function instead of direct navigation
      logger.info(
        `👀 Worker #${worker.id} processing job for ${occupation.title}...`
      );

      const result = await processJobForOccupation(worker, occupation, batch);

      if (!result.success) {
        logger.warn(`⚠️ Failed to process job for ${occupation.title}`);
        continue; // Skip to the next occupation if processing fails
      }

      // Step 4: Handle CAPTCHA if detected with improved detection
      const captchaService = new CaptchaService();
      const captchaResult = await captchaService.checkAndHandleCaptcha(
        worker.page,
        { id: worker.id, captchaCount: worker.captchaCount }
      );

      if (captchaResult.captchaDetected) {
        // Increment CAPTCHA count
        worker.captchaCount++;

        if (!captchaResult.handled) {
          // Rotate proxy using the WorkerPool's method
          logger.info(
            `🔄 Rotating proxy for worker #${worker.id} due to CAPTCHA...`
          );
          await workerPool.rotateProxyForWorker(worker);

          // Use the calculated backoff time
          logger.info(
            `⏳ Worker #${worker.id} waiting ${Math.round(captchaResult.backoffTime / 1000)} seconds to avoid Bing CAPTCHA...`
          );
          await delay(captchaResult.backoffTime);

          // Release the worker before breaking out of the loop
          workerPool.releaseWorker(worker.id);

          // Return early with what we've found so far
          const lastOccupation = batch.occupations.find(
            (o) => o.id === lastProcessedOccupationId
          );

          // Log the lastProcessedOccupationId for debugging
          logger.info(
            `💾 Returning early with lastProcessedOccupationId: ${lastProcessedOccupationId}`
          );

          return {
            batchId: batch.batchId,
            occupationTitle: lastOccupation?.title || null,
            cityName: batch.cityName || null,
            stateCode: batch.stateCode || null,
            success: true,
            jobsFound: totalJobsFound,
            jobsSaved: 0, // We'll save the jobs in the next run
            lastProcessedOccupationId,
            completedOccupationIds: batch.occupations.map((o) => String(o.id)), // <-- NEW
          };
        }
      }

      // Use the jobs from the processJobForOccupation result
      const occupationJobs = result.jobs;

      logger.info(
        `💾 Found ${occupationJobs.length} jobs for ${occupation.title} in ${batch.cityName}`
      );

      // Add to our collection of all jobs
      allJobs = [...allJobs, ...occupationJobs];
      totalJobsFound += occupationJobs.length;

      // Update the last processed occupation ID
      lastProcessedOccupationId = occupation.id;
      logger.info(
        `💾 Worker #${worker.id} successfully processed occupation: ${occupation.title} (ID: ${occupation.id})`
      );

      // Save progress after each occupation is processed
      try {
        const progressTracker = new ProgressTracker("parallelJobScraper");
        const metadata = {
          timestamp: new Date().toISOString(),
          lastBatchId: batch.batchId,
          lastOccupationTitle: occupation.title,
          // We no longer store occupationId in metadata
          lastCityName: batch.cityName,
          lastStateCode: batch.stateCode,
          processedBy: "parallelJobScraper",
          jobsFound: occupationJobs.length,
          totalJobsFound: totalJobsFound,
        };
        // Store both the occupation ID in metadata and the index in the batch
        // This ensures we have both pieces of information for proper progress tracking
        const occupationIndex = batch.occupations.findIndex(
          (o) => o.id === occupation.id
        );

        // Get the current progress to determine the next index
        const currentProgress = await progressTracker.getLatestProgress();
        let nextOccupationIndex = 0;

        if (currentProgress) {
          // If we have a current progress record, increment the occupation index
          const currentOccupationIndex = Number(
            currentProgress.lastOccupationIndex || 0
          );
          nextOccupationIndex = currentOccupationIndex + 1;
          logger.info(
            `💾 Incrementing occupation index from ${currentOccupationIndex} to ${nextOccupationIndex}`
          );
        } else {
          // If no progress record exists, use the batch index + 1
          nextOccupationIndex = occupationIndex + 1;
          logger.info(
            `💾 No progress record found, using batch index + 1: ${nextOccupationIndex}`
          );
        }

        await progressTracker.updateProgress({
          // Use an incremented index to ensure we're making progress
          lastOccupationIndex: nextOccupationIndex,
          lastCityIndex: batch.cityIndex,
          metadata,
        });

        if (occupationJobs.length > 0) {
          logger.info(
            `💾 Saved progress for occupation: ${occupation.title} (${occupation.id}) with ${occupationJobs.length} jobs found`
          );
        } else {
          logger.info(
            `⏭️ Skipping occupation: ${occupation.title} (${occupation.id}) - No jobs found in ${batch.cityName}`
          );
        }
      } catch (error) {
        logger.error(`❌ Error saving progress for occupation:`, error);
      }

      // Add a random delay between occupations to avoid rate limiting and appear more human-like
      const betweenOccupationsDelay = 3000 + Math.floor(Math.random() * 7000); // 3-10 seconds
      logger.info(
        `⏳ Adding random delay of ${Math.round(betweenOccupationsDelay / 1000)}s between occupations...`
      );
      await delay(betweenOccupationsDelay);
    }

    // Step 7: Save the jobs if found
    let savedCount = 0;
    if (totalJobsFound > 0) {
      try {
        logger.info(
          `💾 Attempting to save ${totalJobsFound} jobs using JobService...`
        );

        // Debug: Check if jobService is still valid
        if (!jobService) {
          logger.error("❌ JobService is undefined when trying to save jobs");
          throw new Error("JobService is undefined");
        }

        // Use the JobService to deduplicate and save jobs
        logger.info(`🔄 Deduplicating ${allJobs.length} jobs...`);
        const uniqueJobs = jobService.deduplicateJobs(allJobs);
        logger.info(`✅ Deduplicated to ${uniqueJobs.length} unique jobs`);

        // Save jobs to the database
        logger.info(
          `💾 Saving ${uniqueJobs.length} unique jobs to database...`
        );
        const saveResult = await jobService.saveJobs(uniqueJobs);
        savedCount =
          typeof saveResult === "object" ? saveResult.saved : saveResult;
        logger.info(`💾 Saved ${savedCount} jobs to database`);
      } catch (saveError) {
        // Log the error but don't fail the batch
        logger.error(`❌ Error saving jobs to database: ${saveError}`);
        logger.error(`❌ Error details:`, saveError);
        // Continue processing - we'll return success even if saving fails
      }
    } else {
      // No jobs found for any occupations in this batch
      logger.info(
        `ℹ️ No jobs found for batch ${batch.batchId} in ${batch.cityName} - marking all ${batch.occupations.length} occupations as completed`
      );

      // Log each occupation being marked as completed
      batch.occupations.forEach((occupation) => {
        logger.info(
          `✅ Marking occupation ${occupation.title} (ID: ${occupation.id}) as completed - no jobs found`
        );
      });
    }

    const lastOccupation = batch.occupations.find(
      (o) => o.id === lastProcessedOccupationId
    );

    // Release the worker before returning
    if (worker) {
      workerPool.releaseWorker(worker.id);
    }

    // Return the batch result
    return {
      batchId: batch.batchId,
      occupationTitle: lastOccupation?.title || null,
      cityName: batch.cityName || null,
      stateCode: batch.stateCode || null,
      success: true,
      jobsFound: totalJobsFound,
      jobsSaved: savedCount,
      lastProcessedOccupationId: lastOccupation?.id || undefined,
      completedOccupationIds: batch.occupations.map((o) => String(o.id)), // <-- NEW
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      `❌ Error in processing batch: ${batch.batchId} - ${errorMessage}`
    );

    // Release the worker in case of error
    if (worker) {
      workerPool.releaseWorker(worker.id);
    }

    return {
      batchId: batch.batchId,
      occupationTitle: null,
      cityName: batch.cityName,
      stateCode: batch.stateCode,
      success: false,
      jobsFound: 0,
      jobsSaved: 0,
      error: errorMessage,
      completedOccupationIds: batch.occupations.map((o) => String(o.id)),
    };
  }
}

export async function processJobForOccupation(
  worker: Worker,
  occupation: { id: string; title: string },
  batch: JobBatch
): Promise<{
  success: boolean;
  jobs: any[];
  captchaDetected?: boolean;
}> {
  try {
    logger.info(
      `🔁 Processing job ${occupation.title} for batch ${batch.batchId} on worker #${worker.id}`
    );

    // Update the current job being processed
    worker.currentJob = {
      title: occupation.title,
      id: occupation.id,
    };

    // Parse job listings
    logger.info(
      `🔍 Searching for jobs for ${occupation.title} in ${batch.cityName}, ${batch.stateCode}`
    );
    const jobs = await searchBingForJobs(
      worker.page,
      occupation.title,
      batch.cityName,
      batch.stateCode
    );

    // Add job details to the jobs
    const jobService = new JobService();
    const cleanedJobs = await Promise.all(
      jobs.map((job: JobData) =>
        jobService.cleanJobData({
          ...job,
          platform: "bing", // Add the required platform property
        })
      )
    );

    const jobsWithOccupation = cleanedJobs.map((job) => ({
      ...job,
      occupationId: occupation.id,
      occupationTitle: occupation.title,
      cityId: batch.cityId,
      cityName: batch.cityName,
      stateId: batch.stateId,
      stateCode: batch.stateCode,
      stateName: batch.stateName,
    }));

    if (jobs.length > 0) {
      logger.info(
        `✅ Successfully processed job for occupation ${occupation.title}`
      );
      return { success: true, jobs: jobsWithOccupation };
    } else {
      return { success: true, jobs: [] };
    }
  } catch (error) {
    // Check if this is a captcha-related error
    const errorMessage = error instanceof Error ? error.message : String(error);
    const isCaptchaError = errorMessage.toLowerCase().includes("captcha");

    logger.error(
      `❌ Error processing job for occupation ${occupation.title}: ${errorMessage}`
    );

    return {
      success: false,
      jobs: [],
      captchaDetected: isCaptchaError,
    };
  }
}
